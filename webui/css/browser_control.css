/* Browser Manual Control Styles */

/* Control button container */
.browser-control-div {
    margin: 10px 0;
    padding: 10px;
    background: linear-gradient(135deg, #2a4a6b, #1a3a5b);
    border: 1px solid #3a5a7b;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Control button */
.browser-control-btn {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.browser-control-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #5ba0f2, #4580cd);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.browser-control-btn:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* Active control state */
.browser-control-btn.control-active {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    cursor: not-allowed;
    opacity: 0.8;
}

.browser-control-btn:disabled {
    cursor: not-allowed;
    opacity: 0.8;
}

.browser-control-btn .material-symbols-outlined {
    font-size: 18px;
}

/* Status text */
.browser-control-status {
    color: #a0c4ff;
    font-size: 13px;
    font-style: italic;
}

.browser-control-status.control-in-use {
    color: #ff6b6b;
    font-weight: 600;
}

.intervention-suggested {
    color: #ffa726;
    font-weight: 600;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        text-shadow: 0 0 5px #ffa726;
    }
    to {
        text-shadow: 0 0 10px #ffa726, 0 0 15px #ffa726;
    }
}

/* Modal styles for browser control */
.browser-control-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #1a1a1a;
    color: white;
}

.browser-control-header {
    background: #2a2a2a;
    padding: 15px 20px;
    border-bottom: 1px solid #444;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.control-info {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.control-info .material-symbols-outlined {
    color: #4a90e2;
    font-size: 20px;
}

.control-timer {
    background: #333;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 12px;
    margin-left: 10px;
}

.control-timer.timer-warning {
    background: #e74c3c;
    color: white;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.control-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.control-action-btn {
    background: #444;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
    transition: background 0.2s ease;
}

.control-action-btn:hover {
    background: #555;
}

.control-action-btn.release-btn {
    background: #e74c3c;
}

.control-action-btn.release-btn:hover {
    background: #c0392b;
}

.control-action-btn .material-symbols-outlined {
    font-size: 16px;
}

/* VNC container */
.browser-control-body {
    flex: 1;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

#vnc-frame {
    width: 100%;
    height: 100%;
    border: none;
}

/* Footer */
.browser-control-footer {
    background: #2a2a2a;
    padding: 10px 20px;
    border-top: 1px solid #444;
    flex-shrink: 0;
}

.control-help {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #aaa;
}

.control-help .material-symbols-outlined {
    color: #4a90e2;
    font-size: 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .browser-control-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
    
    .control-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .browser-control-btn {
        font-size: 13px;
        padding: 10px 16px;
    }
}

/* Dark mode adjustments */
.dark-mode .browser-control-div {
    background: linear-gradient(135deg, #2a4a6b, #1a3a5b);
    border-color: #3a5a7b;
}

.dark-mode .browser-control-status {
    color: #a0c4ff;
}

/* Light mode adjustments */
.light-mode .browser-control-div {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-color: #90caf9;
    color: #1565c0;
}

.light-mode .browser-control-btn {
    background: linear-gradient(135deg, #1976d2, #1565c0);
}

.light-mode .browser-control-status {
    color: #1565c0;
}

/* Animation for button appearance */
.browser-control-div {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}