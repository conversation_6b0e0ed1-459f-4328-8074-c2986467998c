"""
Browser Service Manager for Manual Control
Manages headed browser instances with VNC access for user intervention
"""

import os
import subprocess
import asyncio
import time
import psutil
from typing import Optional, Dict, Any
import json
from python.helpers.log import Log
from python.helpers.errors import format_error


class BrowserService:
    """Manages headed browser instances with VNC access"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.xvfb_process: Optional[subprocess.Popen] = None
        self.browser_process: Optional[subprocess.Popen] = None
        self.vnc_server: Optional[subprocess.Popen] = None
        self.novnc_server: Optional[subprocess.Popen] = None
        self.display = ":99"
        self.vnc_port = self.config.get("vnc_port", 5900)
        self.novnc_port = self.config.get("novnc_port", 6080)
        self.cdp_port = self.config.get("cdp_port", 9222)
        self.screen_width = self.config.get("screen_width", 1920)
        self.screen_height = self.config.get("screen_height", 1080)
        self.user_data_dir = "/tmp/browser_profile_manual"
        self.is_running = False
        
    async def start(self) -> Dict[str, Any]:
        """Start the headed browser service with all components"""
        try:
            if self.is_running:
                return self.get_connection_info()
                
            Log().log(type="info", content="Starting browser service components...")
            
            # Start Xvfb (X Virtual Framebuffer)
            await self._start_xvfb()
            
            # Start window manager (lightweight)
            await self._start_window_manager()
            
            # Start Chrome/Chromium with debugging
            await self._start_browser()
            
            # Start VNC server
            await self._start_vnc()
            
            # Start noVNC web server
            await self._start_novnc()
            
            self.is_running = True
            
            connection_info = self.get_connection_info()
            Log().log(type="info", content=f"Browser service started: {connection_info}")
            return connection_info
            
        except Exception as e:
            await self.stop()
            raise Exception(f"Failed to start browser service: {format_error(e)}")
    
    async def _start_xvfb(self):
        """Start X Virtual Framebuffer"""
        # Check if Xvfb is already running
        try:
            result = subprocess.run(['pgrep', 'Xvfb'], capture_output=True)
            if result.returncode == 0:
                Log().log(type="info", content="Xvfb already running")
                os.environ['DISPLAY'] = self.display
                return
        except:
            pass
        
        xvfb_cmd = [
            'Xvfb',
            self.display,
            '-screen', '0',
            f'{self.screen_width}x{self.screen_height}x24',
            '-ac',  # Disable access control
            '+extension', 'GLX',
            '+render',
            '-noreset'
        ]
        
        Log().log(type="info", content=f"Starting Xvfb: {' '.join(xvfb_cmd)}")
        
        try:
            self.xvfb_process = subprocess.Popen(
                xvfb_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Set display environment variable
            os.environ['DISPLAY'] = self.display
            
            # Wait for Xvfb to start and check if it's running
            await asyncio.sleep(2)
            
            if self.xvfb_process.poll() is not None:
                # Process died, get error info
                stdout, stderr = self.xvfb_process.communicate()
                raise Exception(f"Xvfb failed to start: {stderr.decode()}")
                
            Log().log(type="info", content=f"Xvfb started on {self.display}")
            
        except Exception as e:
            Log().log(type="error", content=f"Failed to start Xvfb: {e}")
            raise
        
        if self.xvfb_process.poll() is not None:
            raise Exception("Xvfb failed to start")
    
    async def _start_window_manager(self):
        """Start a lightweight window manager"""
        # Using fluxbox as it's lightweight
        fluxbox_cmd = ['fluxbox', '-display', self.display]
        
        subprocess.Popen(
            fluxbox_cmd,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            env={**os.environ, 'DISPLAY': self.display}
        )
        
        await asyncio.sleep(0.5)
    
    async def _start_browser(self):
        """Start Chrome/Chromium with remote debugging"""
        # Try to find Chrome/Chromium binary
        chrome_binary = self._find_chrome_binary()
        
        if not chrome_binary:
            raise Exception("Chrome/Chromium not found")
        
        browser_cmd = [
            chrome_binary,
            f'--remote-debugging-port={self.cdp_port}',
            '--remote-debugging-address=0.0.0.0',
            f'--user-data-dir={self.user_data_dir}',
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-default-apps',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-web-security',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
            '--window-size={},{}'.format(self.screen_width, self.screen_height),
            '--window-position=0,0',
            '--enable-logging',
            '--log-level=0',
            'about:blank'
        ]
        
        self.browser_process = subprocess.Popen(
            browser_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            env={**os.environ, 'DISPLAY': self.display}
        )
        
        # Wait for browser to start and CDP to be available
        await self._wait_for_cdp()
    
    async def _start_vnc(self):
        """Start VNC server"""
        # Check if VNC is already running on this port
        try:
            result = subprocess.run(['pgrep', '-f', f'x11vnc.*rfbport.*{self.vnc_port}'], capture_output=True)
            if result.returncode == 0:
                Log().log(type="info", content=f"VNC server already running on port {self.vnc_port}")
                return
        except:
            pass
        
        vnc_cmd = [
            'x11vnc',
            '-display', self.display,
            '-nopw',  # No password for simplicity (add auth later) 
            '-forever',
            '-shared',
            '-rfbport', str(self.vnc_port),
            '-listen', '0.0.0.0',  # Listen on all interfaces
            '-bg'
        ]
        
        Log().log(type="info", content=f"Starting VNC server: {' '.join(vnc_cmd)}")
        
        try:
            result = subprocess.run(
                vnc_cmd,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode != 0:
                raise Exception(f"VNC server failed to start: {result.stderr}")
            
            # VNC starts in background due to -bg flag, so we just wait a bit
            await asyncio.sleep(2)
            
            # Verify VNC is running
            check_result = subprocess.run(['pgrep', '-f', f'x11vnc.*rfbport.*{self.vnc_port}'], capture_output=True)
            if check_result.returncode != 0:
                raise Exception("VNC server process not found after startup")
            
            Log().log(type="info", content=f"VNC server started on port {self.vnc_port}")
            
        except Exception as e:
            Log().log(type="error", content=f"Failed to start VNC server: {e}")
            raise
    
    async def _start_novnc(self):
        """Start noVNC web server for browser-based VNC access"""
        # Check if websockify is already running on this port
        try:
            result = subprocess.run(['pgrep', '-f', f'websockify.*{self.novnc_port}'], capture_output=True)
            if result.returncode == 0:
                Log().log(type="info", content=f"websockify already running on port {self.novnc_port}")
                return
        except:
            pass
        
        novnc_cmd = [
            'websockify',
            '--web', '/usr/share/novnc',
            f'0.0.0.0:{self.novnc_port}',  # Bind to all interfaces
            f'localhost:{self.vnc_port}'
        ]
        
        Log().log(type="info", content=f"Starting websockify: {' '.join(novnc_cmd)}")
        
        try:
            self.novnc_server = subprocess.Popen(
                novnc_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Wait and check if process is still running
            await asyncio.sleep(3)
            
            if self.novnc_server.poll() is not None:
                # Process died, get error info
                stdout, stderr = self.novnc_server.communicate()
                raise Exception(f"websockify failed to start: {stderr.decode()}")
            
            Log().log(type="info", content=f"websockify started on port {self.novnc_port}")
            
        except Exception as e:
            Log().log(type="error", content=f"Failed to start websockify: {e}")
            raise
    
    async def _wait_for_cdp(self, timeout: int = 10):
        """Wait for Chrome DevTools Protocol to be available"""
        import aiohttp
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(f'http://localhost:{self.cdp_port}/json') as resp:
                        if resp.status == 200:
                            return
            except:
                pass
            await asyncio.sleep(0.5)
        
        raise Exception(f"CDP not available after {timeout} seconds")
    
    def _find_chrome_binary(self) -> Optional[str]:
        """Find Chrome or Chromium binary"""
        # Docker/Linux paths first
        possible_paths = [
            '/usr/bin/chromium',
            '/usr/bin/chromium-browser',
            '/usr/bin/google-chrome-stable',
            '/usr/bin/google-chrome',
            '/opt/google/chrome/chrome',
            '/snap/bin/chromium',
            # macOS paths (for development)
            '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # Try to find via which command
        for name in ['chromium', 'chromium-browser', 'google-chrome']:
            try:
                result = subprocess.run(
                    ['which', name],
                    capture_output=True,
                    text=True
                )
                if result.returncode == 0:
                    return result.stdout.strip()
            except:
                pass
        
        return None
    
    async def stop(self):
        """Stop all browser service components"""
        Log().log(type="info", content="Stopping browser service...")
        
        processes = [
            ('noVNC', self.novnc_server),
            ('VNC', self.vnc_server),
            ('Browser', self.browser_process),
            ('Xvfb', self.xvfb_process)
        ]
        
        for name, proc in processes:
            if proc:
                try:
                    proc.terminate()
                    try:
                        proc.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        proc.kill()
                    Log().log(type="info", content=f"Stopped {name}")
                except Exception as e:
                    Log().log(type="error", content=f"Error stopping {name}: {e}")
        
        self.is_running = False
    
    def get_connection_info(self) -> Dict[str, Any]:
        """Get connection information for the browser service"""
        return {
            "cdp_url": f"ws://localhost:{self.cdp_port}",
            "vnc_port": self.vnc_port,
            "novnc_url": f"http://localhost:{self.novnc_port}/vnc.html?host=localhost&port={self.novnc_port}",
            "display": self.display,
            "is_running": self.is_running
        }
    
    def is_healthy(self) -> bool:
        """Check if all components are running"""
        if not self.is_running:
            return False
        
        # Check if processes are still running
        processes = [
            self.xvfb_process,
            self.browser_process,
            self.vnc_server,
            self.novnc_server
        ]
        
        for proc in processes:
            if proc and proc.poll() is not None:
                return False
        
        return True
    
    async def restart(self):
        """Restart the browser service"""
        await self.stop()
        await asyncio.sleep(2)
        await self.start()


# Singleton instance management
_browser_service_instance: Optional[BrowserService] = None


async def get_browser_service(config: Dict[str, Any] = None) -> BrowserService:
    """Get or create the browser service singleton"""
    global _browser_service_instance
    
    if _browser_service_instance is None:
        _browser_service_instance = BrowserService(config)
    
    return _browser_service_instance


async def start_browser_service(config: Dict[str, Any] = None) -> Dict[str, Any]:
    """Convenience function to start the browser service"""
    service = await get_browser_service(config)
    return await service.start()


async def stop_browser_service():
    """Convenience function to stop the browser service"""
    global _browser_service_instance
    
    if _browser_service_instance:
        await _browser_service_instance.stop()
        _browser_service_instance = None